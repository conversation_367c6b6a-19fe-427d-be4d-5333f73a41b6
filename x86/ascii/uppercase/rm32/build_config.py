build_config = {
  "projects": {
    u'x86\\ascii\\uppercase\\rm32': {
      "files": {
        u'EAX.bin': {
          "sources": [u'EAX.asm']
        },
        u'EBP.bin': {
          "sources": [u'EBP.asm']
        },
        u'EBX.bin': {
          "sources": [u'EBX.asm']
        },
        u'ECX.bin': {
          "sources": [u'ECX.asm']
        },
        u'EDI.bin': {
          "sources": [u'EDI.asm']
        },
        u'EDX.bin': {
          "sources": [u'EDX.asm']
        },
        u'ESI.bin': {
          "sources": [u'ESI.asm']
        },
        u'ESP.bin': {
          "sources": [u'ESP.asm']
        },
        u'[EAX].bin': {
          "sources": [u'[EAX].asm']
        },
        u'[EBP].bin': {
          "sources": [u'[EBP].asm']
        },
        u'[EBX].bin': {
          "sources": [u'[EBX].asm']
        },
        u'[ECX].bin': {
          "sources": [u'[ECX].asm']
        },
        u'[EDI].bin': {
          "sources": [u'[EDI].asm']
        },
        u'[EDX].bin': {
          "sources": [u'[EDX].asm']
        },
        u'[ESI].bin': {
          "sources": [u'[ESI].asm']
        },
        u'[ESP].bin': {
          "sources": [u'[ESP].asm']
        }
      }
    }
  }
}
